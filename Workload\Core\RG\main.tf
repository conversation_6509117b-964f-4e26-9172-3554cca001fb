# Azure Firewall
module "azure_firewall" {
  source = "../../../Modules/Firewall/firewall"

  firewallName        = var.azure_firewall.name
  location            = local.location
  rgName              = local.hub_rg_name
  skuName             = var.azure_firewall.sku_name
  skuTier             = var.azure_firewall.sku_tier
  zones               = var.azure_firewall.zones
  tags                = merge(local.common_tags, var.azure_firewall.tags)
  ipConfigurationName = var.azure_firewall.ip_configuration_name
  subnetId            = data.azurerm_subnet.hub_firewall_subnet.id
  publicIpId          = data.azurerm_public_ip.firewall_pip.id
}

# Azure Bastion
module "azure_bastion" {
  source = "../../../Modules/Network/bastion"

  rgName              = local.hub_rg_name
  location            = local.location
  bastionSettings = {
    bastionName         = var.azure_bastion.name
    sku                 = var.azure_bastion.bastion_settings.sku
    ipConfigurationName = var.azure_bastion.bastion_settings.ip_configuration_name
    scaleUnits          = var.azure_bastion.bastion_settings.scale_units
  }
  bastionConfig = {
    copyPasteEnabled = var.azure_bastion.bastion_config.copy_paste_enabled
    fileCopyEnabled  = var.azure_bastion.bastion_config.file_copy_enabled
    tunnelingEnabled = var.azure_bastion.bastion_config.tunneling_enabled
  }
  subnetId          = data.azurerm_subnet.hub_bastion_subnet.id
  publicIpAddressId = data.azurerm_public_ip.bastion_pip.id
  tags              = merge(local.common_tags, var.azure_bastion.tags)
}

# Public IP for Application Gateway
module "app_gateway_public_ip" {
  source = "../../../Modules/Network/publicIp"

  publicIpName     = "${var.application_gateway.name}-PublicIP"
  location         = local.location
  rgName           = local.hub_rg_name
  allocationMethod = "Static"
  publicIpSKU      = "Standard"
  zones            = ["1"]
  tags             = merge(local.common_tags, var.application_gateway.tags, { Purpose = "Application Gateway" })
}

# Application Gateway
module "application_gateway" {
  source = "../../../Modules/Network/applicationGateway"

  applicationGatewayName          = var.application_gateway.name
  rgName                          = local.hub_rg_name
  location                        = local.location
  sku                             = var.application_gateway.sku
  tier                            = var.application_gateway.tier
  capacity                        = var.application_gateway.capacity
  gatewayIpConfiguration          = var.application_gateway.gateway_ip_configuration
  subnetId                        = data.azurerm_subnet.hub_normal_subnet.id
  frontendPortName                = var.application_gateway.frontend_port_name
  port                            = var.application_gateway.port
  frontendIpConfigurationName     = var.application_gateway.frontend_ip_configuration_name
  publicIpId                      = module.app_gateway_public_ip.publicIpId
  backendAddressPoolName          = var.application_gateway.backend_address_pool_name
  httpSettingName                 = var.application_gateway.http_setting_name
  cookieBasedAffinity             = var.application_gateway.cookie_based_affinity
  path                            = var.application_gateway.path
  protocol                        = var.application_gateway.protocol
  requestTimeout                  = var.application_gateway.request_timeout
  listenerName                    = var.application_gateway.listener_name
  requestRoutingRuleName          = var.application_gateway.request_routing_rule_name
  ruleType                        = var.application_gateway.rule_type
  priority                        = var.application_gateway.priority
  apimTags                        = merge(local.common_tags, var.application_gateway.tags)

  depends_on = [module.app_gateway_public_ip]
}

# Private DNS Zones
module "private_dns_zones" {
  for_each = var.private_dns_zones
  source   = "../../../Modules/Network/privateDnsZone"

  privateDnsZoneName = each.value.name
  rgName             = local.hub_rg_name
  tags               = merge(local.common_tags, each.value.tags)
}

# Virtual Network Links for Private DNS Zones
module "vnet_links" {
  for_each = var.vnet_links
  source   = "../../../Modules/Network/Vnet/virtualNetworkLink"

  privateDnsLinkName                = each.value.name
  privateDnsLinkRegistrationEnabled = each.value.registration_enabled
  rgName                            = local.hub_rg_name
  privateDnsLinkZoneName            = module.private_dns_zones[each.value.dns_zone_key].privateDnsZoneName
  privateDnsLinkVirtualNetworkId    = local.hub_vnet_id
  tags                              = merge(local.common_tags, each.value.tags)

  depends_on = [module.private_dns_zones]
}