resource "azurerm_subnet" "subnets" {
  name                                          = var.subnetName
  resource_group_name                           = var.rgName
  address_prefixes                              = var.subnetAddressPrefixes
  virtual_network_name                          = var.virtualNetworkName
  private_link_service_network_policies_enabled = var.privateLinkServiceNetworkPoliciesEnabled
  //service_endpoints                             = var.serviceEndpoints
  dynamic "delegation" {
    for_each = var.subnetDelegations != null ? [var.subnetDelegations] : []
    content {
      name = delegation.value.subnetDelegationName
      service_delegation {
        name    = delegation.value.serviceDelegationName
        actions = [delegation.value.actions]
      }
    }
  }
}

resource "azurerm_subnet_network_security_group_association" "nsgAssociation" {
  count                     = var.nsgId == null ? 0 : 1
  subnet_id                 = azurerm_subnet.subnets.id
  network_security_group_id = var.nsgId
  depends_on                = [azurerm_subnet.subnets]
}

resource "azurerm_subnet_route_table_association" "rtAssociation" {
  count          = var.rtId == null ? 0 : 1
  subnet_id      = azurerm_subnet.subnets.id
  route_table_id = var.rtId
  depends_on     = [azurerm_subnet.subnets]
}
