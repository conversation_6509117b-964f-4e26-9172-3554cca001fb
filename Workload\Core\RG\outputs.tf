# Azure Firewall Outputs
output "firewall_id" {
  description = "Azure Firewall ID"
  value       = module.azure_firewall.firewallId
}

output "firewall_name" {
  description = "Azure Firewall name"
  value       = module.azure_firewall.firewallName
}

# Azure Bastion Outputs
output "bastion_id" {
  description = "Azure Bastion ID"
  value       = module.azure_bastion.bastionId
}

output "bastion_name" {
  description = "Azure Bastion name"
  value       = module.azure_bastion.bastionName
}

output "bastion_dns_name" {
  description = "Azure Bastion DNS name"
  value       = module.azure_bastion.bastionDnsName
}

# Application Gateway Outputs
output "app_gateway_id" {
  description = "Application Gateway ID"
  value       = module.application_gateway.applicationGatewayId
}

output "app_gateway_name" {
  description = "Application Gateway name"
  value       = module.application_gateway.applicationGatewayName
}

output "app_gateway_public_ip_id" {
  description = "Application Gateway Public IP ID"
  value       = module.app_gateway_public_ip.publicIpId
}

# Private DNS Zone Outputs
output "private_dns_zone_ids" {
  description = "Map of Private DNS Zone IDs"
  value = {
    for k, v in module.private_dns_zones : k => v.privateDnsZoneId
  }
}

output "private_dns_zone_names" {
  description = "Map of Private DNS Zone names"
  value = {
    for k, v in module.private_dns_zones : k => v.privateDnsZoneName
  }
}

# VNet Link Outputs
output "vnet_link_ids" {
  description = "Map of VNet Link IDs"
  value = {
    for k, v in module.vnet_links : k => v.virtualNetworkLinkId
  }
}

output "vnet_link_names" {
  description = "Map of VNet Link names"
  value = {
    for k, v in module.vnet_links : k => v.virtualNetworkLinkName
  }
}

# Hub Infrastructure Summary
output "hub_infrastructure_summary" {
  description = "Summary of deployed hub infrastructure"
  value = {
    resource_group_name = local.hub_rg_name
    location           = local.location
    hub_vnet_id        = local.hub_vnet_id
    hub_vnet_name      = local.hub_vnet_name
    firewall_deployed  = true
    bastion_deployed   = true
    app_gateway_deployed = true
    dns_zone_count     = length(module.private_dns_zones)
    vnet_link_count    = length(module.vnet_links)
  }
}

# Specific Resource Outputs for Easy Reference (aliases)
output "hub_firewall_id" {
  description = "Hub Firewall ID"
  value       = module.azure_firewall.firewallId
}

output "hub_bastion_id" {
  description = "Hub Bastion ID"
  value       = module.azure_bastion.bastionId
}

output "hub_app_gateway_id" {
  description = "Hub Application Gateway ID"
  value       = module.application_gateway.applicationGatewayId
}

# Private DNS Zone IDs for specific services (useful for private endpoints)
output "acr_dns_zone_id" {
  description = "ACR Private DNS Zone ID"
  value       = module.private_dns_zones["acr_dns_zone"].privateDnsZoneId
}

output "aml_dns_zone_id" {
  description = "AML Private DNS Zone ID"
  value       = module.private_dns_zones["aml_dns_zone"].privateDnsZoneId
}

output "keyvault_dns_zone_id" {
  description = "Key Vault Private DNS Zone ID"
  value       = module.private_dns_zones["keyvault_dns_zone"].privateDnsZoneId
}

output "storage_dns_zone_id" {
  description = "Storage Account Private DNS Zone ID"
  value       = module.private_dns_zones["storage_dns_zone"].privateDnsZoneId
}
