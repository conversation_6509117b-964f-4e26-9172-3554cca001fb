// REQUIRED VARIABLES (variables which are needed to be passed)
variable "subnetName" {
  description = "The Subnet name"
  type        = string
}
variable "rgName" {
  description = "The name of the Azure Resource Group where the Subnet will be created"
  type        = string
}
variable "subnetAddressPrefixes" {
  description = "The address prefixes for the Azure Compute Subnet"
  type        = list(string)
}
variable "virtualNetworkName" {
  description = "The name of the Azure Virtual Network where the Subnet will be created"
  type        = string
}
variable "nsgId" {
  description = "network security group id to associate subnet with"
  type        = string
}
variable "rtId" {
  description = "route table id to associate subnet with"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "privateLinkServiceNetworkPoliciesEnabled" {
  description = "Indicates whether network policies are enabled for private link service on this subnet"
  type        = bool
}
# variable "serviceEndpoints" {
#   description = "The list of service endpoints to associate with the Azure Subnet"
#   type        = list(string)
# }
variable "subnetDelegations" {
  description = "Object of subnet delegations for the Azure Subnet"
  type = object({
    subnetDelegationName  = string
    serviceDelegationName = string
    actions               = string
    })
  nullable = true
}
variable "subnetNsgAssociation" {
  description = "Bool value which defines NSG association with the Subnet"
  type        = bool
}
variable "subnetRtAssociation" {
  description = "Bool value which defines Route Table association with the Subnet"
  type        = bool
}
