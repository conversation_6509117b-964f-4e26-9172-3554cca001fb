{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "app_gateway_public_ip", "Source": "../../../Modules/Network/publicIp", "Dir": "../../../Modules/Network/publicIp"}, {"Key": "application_gateway", "Source": "../../../Modules/Network/applicationGateway", "Dir": "../../../Modules/Network/applicationGateway"}, {"Key": "azure_bastion", "Source": "../../../Modules/Network/bastion", "Dir": "../../../Modules/Network/bastion"}, {"Key": "azure_firewall", "Source": "../../../Modules/Firewall/firewall", "Dir": "../../../Modules/Firewall/firewall"}, {"Key": "private_dns_zones", "Source": "../../../Modules/Network/privateDnsZone", "Dir": "../../../Modules/Network/privateDnsZone"}, {"Key": "vnet_links", "Source": "../../../Modules/Network/Vnet/virtualNetworkLink", "Dir": "../../../Modules/Network/Vnet/virtualNetworkLink"}]}