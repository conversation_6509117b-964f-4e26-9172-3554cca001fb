# Azure Firewall Configuration
azure_firewall = {
  name                   = "Hub-Firewall"
  sku_name              = "AZFW_VNet"
  sku_tier              = "Standard"
  zones                 = ["1", "2", "3"]
  ip_configuration_name = "hub-firewall-ip-config"
  tags = {
    Purpose = "Network Security"
    Tier    = "Hub"
  }
}

# Azure Bastion Configuration
azure_bastion = {
  name = "Hub-Bastion"
  bastion_settings = {
    sku                   = "Standard"
    ip_configuration_name = "hub-bastion-ip-config"
    scale_units          = 2
  }
  bastion_config = {
    copy_paste_enabled = true
    file_copy_enabled  = true
    tunneling_enabled  = true
  }
  tags = {
    Purpose = "Remote Access"
    Tier    = "Hub"
  }
}

# Application Gateway Configuration
application_gateway = {
  name                            = "Hub-Application-Gateway"
  sku                            = "Standard_v2"
  tier                           = "Standard_v2"
  capacity                       = 2
  gateway_ip_configuration       = "hub-appgw-ip-config"
  frontend_port_name             = "hub-appgw-frontend-port"
  port                           = 80
  frontend_ip_configuration_name = "hub-appgw-frontend-ip"
  backend_address_pool_name      = "hub-appgw-backend-pool"
  http_setting_name              = "hub-appgw-http-setting"
  cookie_based_affinity          = "Disabled"
  path                           = "/"
  protocol                       = "Http"
  request_timeout                = 60
  listener_name                  = "hub-appgw-listener"
  request_routing_rule_name      = "hub-appgw-routing-rule"
  rule_type                      = "Basic"
  priority                       = 100
  tags = {
    Purpose = "Application Load Balancing"
    Tier    = "Hub"
  }
}

# Private DNS Zones Configuration
private_dns_zones = {
  "acr_dns_zone" = {
    name = "privatelink.azurecr.io"
    tags = {
      Service = "Azure Container Registry"
      Purpose = "Private DNS Resolution"
    }
  }
  "aml_dns_zone" = {
    name = "privatelink.api.azureml.ms"
    tags = {
      Service = "Azure Machine Learning"
      Purpose = "Private DNS Resolution"
    }
  }
  "keyvault_dns_zone" = {
    name = "privatelink.vaultcore.azure.net"
    tags = {
      Service = "Azure Key Vault"
      Purpose = "Private DNS Resolution"
    }
  }
  "aisearch_dns_zone" = {
    name = "privatelink.search.windows.net"
    tags = {
      Service = "Azure AI Search"
      Purpose = "Private DNS Resolution"
    }
  }
  "cosmosdb_dns_zone" = {
    name = "privatelink.documents.azure.com"
    tags = {
      Service = "Azure Cosmos DB"
      Purpose = "Private DNS Resolution"
    }
  }
  "aks_dns_zone" = {
    name = "privatelink.centralindia.azmk8s.io"
    tags = {
      Service = "Azure Kubernetes Service"
      Purpose = "Private DNS Resolution"
    }
  }
  "storage_dns_zone" = {
    name = "privatelink.blob.core.windows.net"
    tags = {
      Service = "Azure Storage Account"
      Purpose = "Private DNS Resolution"
    }
  }
  "adf_dns_zone" = {
    name = "privatelink.datafactory.azure.net"
    tags = {
      Service = "Azure Data Factory"
      Purpose = "Private DNS Resolution"
    }
  }
}

# Virtual Network Links Configuration
vnet_links = {
  "acr_vnet_link" = {
    name                 = "acr-hub-vnet-link"
    dns_zone_key        = "acr_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Container Registry"
      Purpose = "VNet Integration"
    }
  }
  "aml_vnet_link" = {
    name                 = "aml-hub-vnet-link"
    dns_zone_key        = "aml_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Machine Learning"
      Purpose = "VNet Integration"
    }
  }
  "keyvault_vnet_link" = {
    name                 = "keyvault-hub-vnet-link"
    dns_zone_key        = "keyvault_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Key Vault"
      Purpose = "VNet Integration"
    }
  }
  "aisearch_vnet_link" = {
    name                 = "aisearch-hub-vnet-link"
    dns_zone_key        = "aisearch_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure AI Search"
      Purpose = "VNet Integration"
    }
  }
  "cosmosdb_vnet_link" = {
    name                 = "cosmosdb-hub-vnet-link"
    dns_zone_key        = "cosmosdb_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Cosmos DB"
      Purpose = "VNet Integration"
    }
  }
  "aks_vnet_link" = {
    name                 = "aks-hub-vnet-link"
    dns_zone_key        = "aks_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Kubernetes Service"
      Purpose = "VNet Integration"
    }
  }
  "storage_vnet_link" = {
    name                 = "storage-hub-vnet-link"
    dns_zone_key        = "storage_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Storage Account"
      Purpose = "VNet Integration"
    }
  }
  "adf_vnet_link" = {
    name                 = "adf-hub-vnet-link"
    dns_zone_key        = "adf_dns_zone"
    registration_enabled = false
    tags = {
      Service = "Azure Data Factory"
      Purpose = "VNet Integration"
    }
  }
}
